import sys
from bark import SAMPLE_RATE, generate_audio
from bark.generation import preload_models
from scipy.io.wavfile import write

def main():
    try:
        print("🔄 جاري تحميل نماذج Bark...")
        preload_models()
        print("✅ تم تحميل النماذج بنجاح.")
    except Exception as e:
        print(f"❌ فشل تحميل النماذج: {e}")
        sys.exit(1)

    text_prompt = (
        "أنهَيتم عامكم الأول بكل فخر. أحيي جهودكم وتفانيكم، "
        "وأتمنى لكم إجازة سعيدة. تذكّروا: نهاية الامتحان ليست نهاية التعلم، "
        "بل بدايته الحقيقية."
    )

    try:
        print("🎤 يتم الآن توليد الصوت باستخدام history_prompt 'v2/ar_speaker_6'...")
        audio_array = generate_audio(
            text_prompt,
            history_prompt="v2/ar_speaker_6"  # صوت عربي افتراضي
        )
        print("✅ تم توليد الصوت بنجاح باستخدام history_prompt.")
    except Exception as e:
        print(f"❌ فشل توليد الصوت باستخدام history_prompt 'v2/ar_speaker_6': {e}")
        try:
            print("🔄 محاولة توليد الصوت بدون history_prompt...")
            audio_array = generate_audio(text_prompt)
            print("✅ تم توليد الصوت بنجاح بدون history_prompt.")
        except Exception as exc:
            print(f"❌ فشل توليد الصوت بدون history_prompt أيضًا: {exc}")
            sys.exit(1)

    output_path = "C:/Users/<USER>/Desktop/sas/generated_voice.wav"
    try:
        write(output_path, SAMPLE_RATE, audio_array)
        print(f"✅ تم حفظ الصوت الجديد في: {output_path}")
    except Exception as e:
        print(f"❌ فشل حفظ الملف: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()